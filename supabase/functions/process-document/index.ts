import 'https://deno.land/x/xhr@0.1.0/mod.ts';
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.50.2';
import OpenAI from 'jsr:@openai/openai';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers':
    'authorization, x-client-info, apikey, content-type',
};

// Helper function to convert ArrayBuffer to base64 safely
function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  const chunkSize = 0x8000; // 32KB chunks to avoid stack overflow
  let binary = '';

  for (let i = 0; i < bytes.length; i += chunkSize) {
    const chunk = bytes.subarray(i, i + chunkSize);
    binary += String.fromCharCode(...chunk);
  }

  return btoa(binary);
}

serve(async (req) => {
  console.log('=== PROCESS DOCUMENT FUNCTION STARTED ===');
  console.log('Request method:', req.method);
  console.log('Request URL:', req.url);

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('Handling CORS preflight request');
    return new Response(null, { headers: corsHeaders });
  }

  let documentId;

  try {
    console.log('Process-document function started');

    // Log request headers for debugging
    const headers = Object.fromEntries(req.headers.entries());
    console.log('Request headers:', headers);

    const requestBody = await req.json();
    console.log('Request body received:', requestBody);

    const { documentId: reqDocumentId, fileName, fileUrl } = requestBody;
    documentId = reqDocumentId;

    if (!documentId || !fileName || !fileUrl) {
      console.error('Missing required parameters:', {
        documentId,
        fileName,
        fileUrl,
      });
      throw new Error(
        'Missing required parameters: documentId, fileName, or fileUrl'
      );
    }

    const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY');
    const SUPABASE_URL = Deno.env.get('SUPABASE_URL');
    const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

    console.log('Environment variables check:', {
      hasOpenAI: !!OPENAI_API_KEY,
      hasSupabaseUrl: !!SUPABASE_URL,
      hasServiceKey: !!SUPABASE_SERVICE_ROLE_KEY,
    });

    if (!OPENAI_API_KEY) {
      console.error('OPENAI_API_KEY is not set');
      throw new Error('OPENAI_API_KEY is not set');
    }

    if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Supabase configuration missing');
      throw new Error('Supabase configuration missing');
    }

    // Initialize Supabase client with service role key for admin access
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
    console.log('Supabase client initialized');

    const client = new OpenAI({
      apiKey: OPENAI_API_KEY,
    });

    console.log(`Processing document: ${fileName} (ID: ${documentId})`);

    // Get the document and verify user has credits
    console.log('Fetching document data...');
    const { data: docData, error: docError } = await supabase
      .from('documents')
      .select('user_id')
      .eq('id', documentId)
      .single();

    if (docError) {
      console.error('Failed to get document data:', docError);
      throw new Error(`Failed to get document data: ${docError.message}`);
    }

    console.log('Document data retrieved:', docData);

    // Check user credits (additional safety check)
    console.log('Checking user credits...');
    const { data: creditsData, error: creditsError } = await supabase
      .from('credits')
      .select('credits_remaining')
      .eq('user_id', docData.user_id)
      .single();

    if (creditsError) {
      console.error('Error fetching credits:', creditsError);
      throw new Error(`Failed to check user credits: ${creditsError.message}`);
    }

    if (!creditsData || creditsData.credits_remaining <= 0) {
      console.error('Insufficient credits for user:', docData.user_id);
      throw new Error('Insufficient credits to process document');
    }

    console.log('Credits check passed:', creditsData);

    // Update document status to processing
    console.log('Updating document status to processing...');
    const { error: updateError } = await supabase
      .from('documents')
      .update({ status: 'processing' })
      .eq('id', documentId);

    if (updateError) {
      console.error('Failed to update document status:', updateError);
      throw new Error(
        `Failed to update document status: ${updateError.message}`
      );
    }

    // Get the file from storage
    console.log('Downloading file from storage:', fileUrl);
    const { data: fileData, error: downloadError } = await supabase.storage
      .from('documents')
      .download(fileUrl);

    if (downloadError) {
      console.error('Error downloading file:', downloadError);
      throw new Error(
        `Failed to download file for processing: ${downloadError.message}`
      );
    }

    console.log(
      'File downloaded successfully, size:',
      fileData?.size || 'unknown'
    );

    // Convert file to base64 safely
    console.log('Converting file to base64...');
    const buffer = await fileData.arrayBuffer();
    const base64 = arrayBufferToBase64(buffer);

    // Determine the file type
    const fileType = fileName.toLowerCase().endsWith('.pdf')
      ? 'application/pdf'
      : fileName.toLowerCase().endsWith('.png')
      ? 'image/png'
      : 'image/jpeg';

    const isPdf = fileType === 'application/pdf';

    console.log(
      `File type detected: ${fileType}, base64 length: ${base64.length}`
    );

    // Call OpenAI Vision API
    console.log('Calling OpenAI API...');
    const response = await client.responses.create({
      model: 'gpt-4.1',
      input: [
        {
          role: 'system',
          content: `You are an expert invoice and receipt data extraction assistant. Analyze the provided document image and extract the following information in JSON format:

{
  "vendor_name": "Company or vendor name",
  "invoice_number": "Invoice or receipt number",
  "invoice_date": "Date in YYYY-MM-DD format",
  "total_amount": "Total amount as a number",
  "tax_amount": "Tax amount as a number (if available)",
  "currency": "Currency code (e.g., USD, EUR)",
  "line_items": [
    {
      "description": "Item description",
      "quantity": "Quantity as number",
      "rate": "Unit price as number",
      "amount": "Line total as number"
    }
  ]
}

If any field is not available or unclear, use null for the value. For amounts, extract only the numeric value without currency symbols. Be as accurate as possible and only extract information that is clearly visible in the document.`,
        },
        {
          role: 'user',
          content: [
            {
              type: 'input_text',
              text: 'Please extract the invoice/receipt data from this document:',
            },
            isPdf
              ? {
                  type: 'input_file',
                  filename: fileName,
                  file_data: `data:${fileType};base64,${base64}`,
                }
              : {
                  type: 'input_image',
                  image_url: `data:${fileType};base64,${base64}`,
                  detail: 'high',
                },
          ],
        },
      ],
      max_output_tokens: 1000,
      temperature: 0.1,
    });

    console.log('OpenAI response received');

    // Parse the extracted data
    let extractedData;
    try {
      const content = response.output_text;
      console.log('Raw AI response:', content);

      // Try to extract JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        extractedData = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('No valid JSON found in AI response');
      }
    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      throw new Error(`Failed to parse AI response: ${parseError.message}`);
    }

    console.log('Extracted data:', extractedData);

    // Save extraction data to database
    console.log('Saving extraction to database...');
    const { data: extraction, error: extractionError } = await supabase
      .from('extractions')
      .insert({
        document_id: documentId,
        user_id: docData.user_id,
        vendor_name: extractedData.vendor_name,
        invoice_number: extractedData.invoice_number,
        invoice_date: extractedData.invoice_date,
        total_amount: extractedData.total_amount,
        tax_amount: extractedData.tax_amount,
        currency: extractedData.currency || 'USD',
        line_items: extractedData.line_items,
        confidence_score: 0.9, // High confidence for AI extraction
        raw_data: extractedData,
      })
      .select()
      .single();

    if (extractionError) {
      console.error('Error saving extraction:', extractionError);
      throw new Error(
        `Failed to save extraction data: ${extractionError.message}`
      );
    }

    // Update document status to completed
    console.log('Updating document status to completed...');
    const { error: completeError } = await supabase
      .from('documents')
      .update({ status: 'completed' })
      .eq('id', documentId);

    if (completeError) {
      console.error('Error updating document to completed:', completeError);
      // Don't throw here as the extraction was successful
    }

    console.log('Document processing completed successfully');

    return new Response(
      JSON.stringify({
        success: true,
        extraction: extraction,
        message: 'Document processed successfully',
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  } catch (error) {
    console.error('=== ERROR IN PROCESS-DOCUMENT FUNCTION ===');
    console.error('Error details:', error);
    console.error('Error message:', error.message);
    console.error('Error stack:', error.stack);

    // Update document status to error if we have documentId
    if (documentId) {
      try {
        const SUPABASE_URL = Deno.env.get('SUPABASE_URL');
        const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get(
          'SUPABASE_SERVICE_ROLE_KEY'
        );

        if (SUPABASE_URL && SUPABASE_SERVICE_ROLE_KEY) {
          const supabase = createClient(
            SUPABASE_URL,
            SUPABASE_SERVICE_ROLE_KEY
          );

          await supabase
            .from('documents')
            .update({ status: 'error' })
            .eq('id', documentId);

          console.log('Document status updated to error');
        }
      } catch (updateError) {
        console.error(
          'Failed to update document status to error:',
          updateError
        );
      }
    }

    return new Response(
      JSON.stringify({
        error: error.message || 'Unknown error occurred',
        success: false,
        details: error.stack || 'No stack trace available',
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});
