
-- Create subscribers table to track subscription information
CREATE TABLE public.subscribers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL UNIQUE,
  stripe_customer_id TEXT,
  subscribed BOOLEAN NOT NULL DEFAULT false,
  subscription_tier TEXT,
  subscription_end TIMESTAMPTZ,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create credits table to track user credits
CREATE TABLE public.credits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  credits_remaining INTEGER NOT NULL DEFAULT 0,
  total_credits INTEGER NOT NULL DEFAULT 0,
  last_refill_date TIMESTAMPTZ,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(user_id)
);

-- Create credit transactions table to track credit usage
CREATE TABLE public.credit_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  document_id UUID REFERENCES documents(id) ON DELETE SET NULL,
  credits_used INTEGER NOT NULL,
  transaction_type TEXT NOT NULL CHECK (transaction_type IN ('usage', 'refill', 'bonus')),
  description TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.subscribers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.credits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.credit_transactions ENABLE ROW LEVEL SECURITY;

-- Create policies for subscribers
CREATE POLICY "select_own_subscription" ON public.subscribers
FOR SELECT
USING (user_id = auth.uid() OR email = auth.email());

CREATE POLICY "update_own_subscription" ON public.subscribers
FOR UPDATE
USING (true);

CREATE POLICY "insert_subscription" ON public.subscribers
FOR INSERT
WITH CHECK (true);

-- Create policies for credits
CREATE POLICY "select_own_credits" ON public.credits
FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "update_own_credits" ON public.credits
FOR UPDATE
USING (user_id = auth.uid());

CREATE POLICY "insert_own_credits" ON public.credits
FOR INSERT
WITH CHECK (user_id = auth.uid());

-- Create policies for credit transactions
CREATE POLICY "select_own_transactions" ON public.credit_transactions
FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "insert_own_transactions" ON public.credit_transactions
FOR INSERT
WITH CHECK (user_id = auth.uid());

-- Create function to initialize user credits
CREATE OR REPLACE FUNCTION public.initialize_user_credits()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
  -- Give new users 3 free credits to start
  INSERT INTO public.credits (user_id, credits_remaining, total_credits, last_refill_date)
  VALUES (NEW.id, 3, 3, now())
  ON CONFLICT (user_id) DO NOTHING;
  
  RETURN NEW;
END;
$$;

-- Create trigger to initialize credits for new users
CREATE TRIGGER on_auth_user_created_credits
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.initialize_user_credits();

-- Create function to refill credits based on subscription
CREATE OR REPLACE FUNCTION public.refill_subscription_credits()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
DECLARE
  user_record RECORD;
  credits_to_add INTEGER;
BEGIN
  -- Loop through active subscribers
  FOR user_record IN 
    SELECT s.user_id, s.subscription_tier, c.last_refill_date
    FROM public.subscribers s
    JOIN public.credits c ON s.user_id = c.user_id
    WHERE s.subscribed = true 
    AND s.subscription_end > now()
    AND (c.last_refill_date IS NULL OR c.last_refill_date < date_trunc('month', now()))
  LOOP
    -- Determine credits based on subscription tier
    credits_to_add := CASE user_record.subscription_tier
      WHEN 'Basic' THEN 10
      WHEN 'Premium' THEN 50
      WHEN 'Enterprise' THEN 200
      ELSE 0
    END;
    
    -- Add credits
    IF credits_to_add > 0 THEN
      UPDATE public.credits 
      SET 
        credits_remaining = credits_remaining + credits_to_add,
        total_credits = total_credits + credits_to_add,
        last_refill_date = now(),
        updated_at = now()
      WHERE user_id = user_record.user_id;
      
      -- Record the transaction
      INSERT INTO public.credit_transactions (user_id, credits_used, transaction_type, description)
      VALUES (user_record.user_id, -credits_to_add, 'refill', 
              'Monthly ' || user_record.subscription_tier || ' subscription refill');
    END IF;
  END LOOP;
END;
$$;
