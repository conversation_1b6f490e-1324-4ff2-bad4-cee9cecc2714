// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://zwkjqjxvxmplptnyigyq.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp3a2pxanh2eG1wbHB0bnlpZ3lxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyNzc2OTUsImV4cCI6MjA2Njg1MzY5NX0.4k1rYjnnW4yLF4O_QNBVIAhLf4A4VzknR7zR0bgkf6A";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});