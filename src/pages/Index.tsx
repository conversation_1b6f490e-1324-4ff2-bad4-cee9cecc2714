import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Upload, FileText, Brain, Download, Shield, Zap } from 'lucide-react';
import AuthModal from '@/components/AuthModal';
import Dashboard from '@/components/Dashboard';
import { useUser } from '@/hooks/useUser';

const Index = () => {
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'signup'>('login');
  const { user, isLoading, signOut } = useUser();

  const handleLogin = () => {
    setAuthMode('login');
    setShowAuthModal(true);
  };

  const handleSignup = () => {
    setAuthMode('signup');
    setShowAuthModal(true);
  };

  const handleAuthSuccess = () => {
    setShowAuthModal(false);
  };

  const handleLogout = async () => {
    await signOut();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (user) {
    return <Dashboard onLogout={handleLogout} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-blue-200 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="h-8 w-8 text-blue-600" />
            <span className="text-2xl font-bold text-gray-900">InvoiceAI</span>
          </div>
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={handleLogin}
              className="text-gray-700 hover:text-blue-600"
            >
              Login
            </Button>
            <Button
              onClick={handleSignup}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Get Started
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
            Extract Invoice Data
            <span className="text-blue-600 block">with AI Precision</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 leading-relaxed max-w-2xl mx-auto">
            Upload invoices and receipts, get structured data instantly. Save
            hours of manual data entry with our intelligent OCR technology.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12">
            <Button
              size="lg"
              onClick={handleSignup}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg"
            >
              Start Extracting Free
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={handleLogin}
              className="border-blue-300 text-blue-700 hover:bg-blue-50 px-8 py-4 text-lg"
            >
              Sign In
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Powerful Features for Every Business
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            From small businesses to enterprise organizations, our AI-powered
            platform scales to meet your document processing needs.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <Card className="border-blue-200 hover:shadow-lg transition-shadow duration-300">
            <CardHeader>
              <Upload className="h-12 w-12 text-blue-600 mb-4" />
              <CardTitle className="text-gray-900">Smart Upload</CardTitle>
              <CardDescription>
                Drag and drop invoices, receipts, and documents. Supports PDF,
                PNG, JPG formats.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-blue-200 hover:shadow-lg transition-shadow duration-300">
            <CardHeader>
              <Brain className="h-12 w-12 text-blue-600 mb-4" />
              <CardTitle className="text-gray-900">AI Extraction</CardTitle>
              <CardDescription>
                Advanced OCR and AI models extract vendor, date, amount, and
                line items automatically.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-blue-200 hover:shadow-lg transition-shadow duration-300">
            <CardHeader>
              <Download className="h-12 w-12 text-blue-600 mb-4" />
              <CardTitle className="text-gray-900">Easy Export</CardTitle>
              <CardDescription>
                Export to CSV, Excel, or integrate with your accounting software
                via API.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-blue-200 hover:shadow-lg transition-shadow duration-300">
            <CardHeader>
              <Shield className="h-12 w-12 text-blue-600 mb-4" />
              <CardTitle className="text-gray-900">Secure & Private</CardTitle>
              <CardDescription>
                Enterprise-grade security with encrypted storage and secure user
                authentication.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-blue-200 hover:shadow-lg transition-shadow duration-300">
            <CardHeader>
              <Zap className="h-12 w-12 text-blue-600 mb-4" />
              <CardTitle className="text-gray-900">Batch Processing</CardTitle>
              <CardDescription>
                Process multiple documents at once for maximum efficiency and
                time savings.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-blue-200 hover:shadow-lg transition-shadow duration-300">
            <CardHeader>
              <FileText className="h-12 w-12 text-blue-600 mb-4" />
              <CardTitle className="text-gray-900">Data Validation</CardTitle>
              <CardDescription>
                Review and edit extracted data with smart validation and
                auto-formatting.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Automate Your Invoice Processing?
          </h2>
          <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
            Join thousands of businesses saving time and reducing errors with
            AI-powered data extraction.
          </p>
          <Button
            size="lg"
            onClick={handleSignup}
            className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold"
          >
            Get Started Today
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center mb-8">
            <FileText className="h-8 w-8 text-blue-400 mr-2" />
            <span className="text-2xl font-bold">InvoiceAI</span>
          </div>
          <div className="text-center text-gray-400">
            <p>&copy; 2024 InvoiceAI. All rights reserved.</p>
            <p className="mt-2">
              Intelligent document processing for modern businesses.
            </p>
          </div>
        </div>
      </footer>

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        mode={authMode}
        onAuthSuccess={handleAuthSuccess}
      />
    </div>
  );
};

export default Index;
