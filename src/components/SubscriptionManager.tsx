
import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Crown, CreditCard, Clock, CheckCircle } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface SubscriptionManagerProps {
  userCredits: any;
  onCreditsUpdate: () => void;
}

const SubscriptionManager = ({ userCredits, onCreditsUpdate }: SubscriptionManagerProps) => {
  const [subscription, setSubscription] = useState<any>(null);
  const [transactions, setTransactions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchSubscriptionData();
    fetchTransactions();
  }, []);

  const fetchSubscriptionData = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: subscriptionData } = await supabase
        .from('subscribers')
        .select('*')
        .eq('user_id', user.id)
        .single();

      setSubscription(subscriptionData);
    } catch (error) {
      console.error('Error fetching subscription:', error);
    }
  };

  const fetchTransactions = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: transactionsData } = await supabase
        .from('credit_transactions')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(10);

      setTransactions(transactionsData || []);
    } catch (error) {
      console.error('Error fetching transactions:', error);
    }
  };

  const handleBuyCredits = async (amount: number, price: number) => {
    setLoading(true);
    try {
      // In a real implementation, this would integrate with Stripe
      // For now, we'll simulate adding credits
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Update credits
      const { error: creditsError } = await supabase
        .from('credits')
        .update({
          credits_remaining: (userCredits?.credits_remaining || 0) + amount,
          total_credits: (userCredits?.total_credits || 0) + amount,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id);

      if (creditsError) throw creditsError;

      // Record transaction
      const { error: transactionError } = await supabase
        .from('credit_transactions')
        .insert({
          user_id: user.id,
          credits_used: -amount,
          transaction_type: 'refill',
          description: `Purchased ${amount} credits for $${price}`
        });

      if (transactionError) throw transactionError;

      toast({
        title: "Credits Added",
        description: `Successfully added ${amount} credits to your account`,
      });

      onCreditsUpdate();
      fetchTransactions();
    } catch (error) {
      console.error('Error buying credits:', error);
      toast({
        title: "Error",
        description: "Failed to purchase credits",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const subscriptionPlans = [
    {
      name: "Basic",
      price: "$9.99/month",
      credits: 10,
      features: ["10 credits per month", "Basic support", "Standard processing speed"]
    },
    {
      name: "Premium",
      price: "$29.99/month",
      credits: 50,
      features: ["50 credits per month", "Priority support", "Faster processing", "Advanced analytics"]
    },
    {
      name: "Enterprise",
      price: "$99.99/month",
      credits: 200,
      features: ["200 credits per month", "24/7 support", "Fastest processing", "Custom integrations", "Dedicated manager"]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Current Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Account Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Credits Remaining:</span>
            <Badge variant="secondary" className="text-lg px-3 py-1">
              {userCredits?.credits_remaining || 0}
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Total Credits Used:</span>
            <span className="text-sm text-gray-600">
              {(userCredits?.total_credits || 0) - (userCredits?.credits_remaining || 0)}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Subscription Status:</span>
            <Badge variant={subscription?.subscribed ? "default" : "outline"}>
              {subscription?.subscribed ? `${subscription.subscription_tier} Plan` : "Free Plan"}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Buy Credits */}
      <Card>
        <CardHeader>
          <CardTitle>Buy Credits</CardTitle>
          <CardDescription>
            Purchase additional credits to process more documents
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="border-2 hover:border-blue-300 transition-colors">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold mb-2">10 Credits</div>
                <div className="text-lg text-blue-600 mb-4">$4.99</div>
                <Button 
                  onClick={() => handleBuyCredits(10, 4.99)}
                  disabled={loading}
                  className="w-full"
                >
                  Buy Now
                </Button>
              </CardContent>
            </Card>
            <Card className="border-2 hover:border-blue-300 transition-colors">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold mb-2">25 Credits</div>
                <div className="text-lg text-blue-600 mb-4">$9.99</div>
                <Button 
                  onClick={() => handleBuyCredits(25, 9.99)}
                  disabled={loading}
                  className="w-full"
                >
                  Buy Now
                </Button>
              </CardContent>
            </Card>
            <Card className="border-2 hover:border-blue-300 transition-colors">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold mb-2">100 Credits</div>
                <div className="text-lg text-blue-600 mb-4">$29.99</div>
                <Button 
                  onClick={() => handleBuyCredits(100, 29.99)}
                  disabled={loading}
                  className="w-full"
                >
                  Buy Now
                </Button>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Subscription Plans */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5" />
            Subscription Plans
          </CardTitle>
          <CardDescription>
            Subscribe for monthly credits and additional benefits
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {subscriptionPlans.map((plan, index) => (
              <Card key={plan.name} className={`relative ${index === 1 ? 'border-blue-500 shadow-lg' : ''}`}>
                {index === 1 && (
                  <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-blue-500">
                    Most Popular
                  </Badge>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-xl">{plan.name}</CardTitle>
                  <div className="text-3xl font-bold text-blue-600">{plan.price}</div>
                  <p className="text-sm text-gray-600">{plan.credits} credits/month</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-2">
                    {plan.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Button 
                    className="w-full" 
                    variant={index === 1 ? "default" : "outline"}
                    disabled={subscription?.subscription_tier === plan.name}
                  >
                    {subscription?.subscription_tier === plan.name ? "Current Plan" : "Subscribe"}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Transaction History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Recent Transactions
          </CardTitle>
        </CardHeader>
        <CardContent>
          {transactions.length === 0 ? (
            <p className="text-center text-gray-500 py-4">No transactions yet</p>
          ) : (
            <div className="space-y-4">
              {transactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-sm">{transaction.description}</p>
                    <p className="text-xs text-gray-500">
                      {new Date(transaction.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <Badge 
                    variant={transaction.transaction_type === 'usage' ? 'destructive' : 'default'}
                  >
                    {transaction.transaction_type === 'usage' ? '-' : '+'}{Math.abs(transaction.credits_used)} credits
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SubscriptionManager;
