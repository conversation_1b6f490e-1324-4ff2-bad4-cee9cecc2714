
import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Download, FileText, Calendar, DollarSign, Building } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import type { J<PERSON> } from "@/integrations/supabase/types";

interface ExtractionResultsProps {
  data: any;
}

interface Extraction {
  id: string;
  vendor_name: string;
  invoice_number: string;
  invoice_date: string;
  total_amount: number;
  tax_amount: number;
  currency: string;
  line_items: Json;
  confidence_score: number;
  created_at: string;
  documents: {
    filename: string;
  };
}

const ExtractionResults = ({ data }: ExtractionResultsProps) => {
  const [recentExtractions, setRecentExtractions] = useState<Extraction[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    fetchRecentExtractions();
  }, [data]);

  const fetchRecentExtractions = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: extractions, error } = await supabase
        .from('extractions')
        .select(`
          *,
          documents (
            filename
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        console.error('Error fetching extractions:', error);
        return;
      }

      setRecentExtractions((extractions as any) || []);
    } catch (error) {
      console.error('Fetch extractions error:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportToCSV = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: extractions, error } = await supabase
        .from('extractions')
        .select(`
          *,
          documents (
            filename
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      if (!extractions || extractions.length === 0) {
        toast({
          title: "No data to export",
          description: "Process some documents first to export data",
          variant: "destructive",
        });
        return;
      }

      const csvHeaders = [
        'Document',
        'Vendor',
        'Invoice Number',
        'Date',
        'Total Amount',
        'Tax Amount',
        'Currency',
        'Processed Date'
      ];

      const csvRows = extractions.map(extraction => [
        (extraction.documents as any)?.filename || '',
        extraction.vendor_name || '',
        extraction.invoice_number || '',
        extraction.invoice_date || '',
        extraction.total_amount?.toString() || '',
        extraction.tax_amount?.toString() || '',
        extraction.currency || '',
        new Date(extraction.created_at).toLocaleDateString()
      ]);

      const csvContent = [
        csvHeaders.join(','),
        ...csvRows.map(row => row.map(cell => `"${cell}"`).join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = window.document.createElement('a');
      a.href = url;
      a.download = `invoice_extractions_${new Date().toISOString().split('T')[0]}.csv`;
      window.document.body.appendChild(a);
      a.click();
      window.document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Export successful",
        description: "CSV file has been downloaded",
      });
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: "Export failed",
        description: "Failed to export data to CSV",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Helper function to safely parse JSON line items
  const parseLineItems = (lineItems: Json): any[] => {
    if (!lineItems) return [];
    if (Array.isArray(lineItems)) return lineItems;
    if (typeof lineItems === 'string') {
      try {
        const parsed = JSON.parse(lineItems);
        return Array.isArray(parsed) ? parsed : [];
      } catch {
        return [];
      }
    }
    return [];
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Extraction Results</CardTitle>
          <CardDescription>Loading recent extractions...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (recentExtractions.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Extraction Results</CardTitle>
          <CardDescription>
            No extractions yet. Upload and process documents to see results here.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 mb-2">No extraction results</p>
            <p className="text-sm text-gray-400">Process some documents to see extracted data</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const latestExtraction = recentExtractions[0];
  const lineItems = parseLineItems(latestExtraction.line_items);

  return (
    <div className="space-y-6">
      {/* Latest Extraction Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Latest Extraction</CardTitle>
              <CardDescription>
                Most recently processed document: {latestExtraction.documents?.filename}
              </CardDescription>
            </div>
            <Button onClick={exportToCSV} className="bg-green-600 hover:bg-green-700">
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div className="flex items-center space-x-3">
              <Building className="h-8 w-8 text-blue-600" />
              <div>
                <p className="text-sm text-gray-500">Vendor</p>
                <p className="font-semibold">{latestExtraction.vendor_name || 'N/A'}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <FileText className="h-8 w-8 text-green-600" />
              <div>
                <p className="text-sm text-gray-500">Invoice #</p>
                <p className="font-semibold">{latestExtraction.invoice_number || 'N/A'}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Calendar className="h-8 w-8 text-purple-600" />
              <div>
                <p className="text-sm text-gray-500">Date</p>
                <p className="font-semibold">
                  {latestExtraction.invoice_date ? formatDate(latestExtraction.invoice_date) : 'N/A'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <DollarSign className="h-8 w-8 text-orange-600" />
              <div>
                <p className="text-sm text-gray-500">Total Amount</p>
                <p className="font-semibold">
                  {latestExtraction.currency} {latestExtraction.total_amount?.toFixed(2) || '0.00'}
                </p>
              </div>
            </div>
          </div>

          {/* Line Items */}
          {lineItems && lineItems.length > 0 && (
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Line Items</h4>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Description</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Rate</TableHead>
                    <TableHead>Amount</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {lineItems.map((item: any, index: number) => (
                    <TableRow key={index}>
                      <TableCell>{item.description || 'N/A'}</TableCell>
                      <TableCell>{item.quantity || 'N/A'}</TableCell>
                      <TableCell>
                        {item.rate ? `${latestExtraction.currency} ${item.rate.toFixed(2)}` : 'N/A'}
                      </TableCell>
                      <TableCell>
                        {item.amount ? `${latestExtraction.currency} ${item.amount.toFixed(2)}` : 'N/A'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Confidence Score */}
          <div className="mt-4 flex items-center space-x-2">
            <span className="text-sm text-gray-500">Confidence Score:</span>
            <Badge variant="secondary">
              {((latestExtraction.confidence_score || 0) * 100).toFixed(0)}%
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Recent Extractions List */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Extractions</CardTitle>
          <CardDescription>
            Your last {recentExtractions.length} processed documents
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Document</TableHead>
                <TableHead>Vendor</TableHead>
                <TableHead>Invoice #</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Date Processed</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recentExtractions.map((extraction) => (
                <TableRow key={extraction.id}>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <FileText className="h-4 w-4 text-blue-600" />
                      <span className="font-medium">{extraction.documents?.filename}</span>
                    </div>
                  </TableCell>
                  <TableCell>{extraction.vendor_name || '-'}</TableCell>
                  <TableCell>{extraction.invoice_number || '-'}</TableCell>
                  <TableCell>
                    {extraction.currency} {extraction.total_amount?.toFixed(2) || '0.00'}
                  </TableCell>
                  <TableCell>{formatDate(extraction.created_at)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default ExtractionResults;
