import { Database } from '@/integrations/supabase/types';

// Extract types from the database schema
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type Credits = Database['public']['Tables']['credits']['Row'];
export type CreditTransaction = Database['public']['Tables']['credit_transactions']['Row'];

// User profile with credits combined
export interface UserProfileWithCredits {
  profile: Profile | null;
  credits: Credits | null;
  isLoading: boolean;
  error: string | null;
}

// Stats interface
export interface UserStats {
  documentsProcessed: number;
  dataPointsExtracted: number;
  exportsGenerated: number;
}
