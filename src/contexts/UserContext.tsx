import React, {
  createContext,
  useEffect,
  useState,
  ReactNode,
  useCallback,
} from 'react';
import { User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { Profile, Credits, UserStats } from '@/types/user';
import { useToast } from '@/hooks/use-toast';

interface UserContextType {
  // Auth state
  user: User | null;
  isLoading: boolean;

  // Profile and credits
  profile: Profile | null;
  credits: Credits | null;
  stats: UserStats;

  // Loading states
  profileLoading: boolean;
  creditsLoading: boolean;
  statsLoading: boolean;

  // Actions
  refreshProfile: () => Promise<void>;
  refreshCredits: () => Promise<void>;
  refreshStats: () => Promise<void>;
  refreshAll: () => Promise<void>;
  signOut: () => Promise<void>;
}

export const UserContext = createContext<UserContextType | undefined>(
  undefined
);

interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [credits, setCredits] = useState<Credits | null>(null);
  const [stats, setStats] = useState<UserStats>({
    documentsProcessed: 0,
    dataPointsExtracted: 0,
    exportsGenerated: 0,
  });

  const [profileLoading, setProfileLoading] = useState(false);
  const [creditsLoading, setCreditsLoading] = useState(false);
  const [statsLoading, setStatsLoading] = useState(false);

  const { toast } = useToast();

  // Initialize auth state
  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      setUser(session?.user ?? null);
      setIsLoading(false);
    };

    getInitialSession();

    // Set up auth state listener
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null);
      setIsLoading(false);

      // Clear user data on sign out
      if (event === 'SIGNED_OUT') {
        setProfile(null);
        setCredits(null);
        setStats({
          documentsProcessed: 0,
          dataPointsExtracted: 0,
          exportsGenerated: 0,
        });
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  // Fetch user data when user changes
  useEffect(() => {
    if (user) {
      refreshAll();
    }
  }, [user, refreshAll]);

  const refreshProfile = useCallback(async () => {
    if (!user) return;

    setProfileLoading(true);
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch profile data',
          variant: 'destructive',
        });
      } else {
        setProfile(profile);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    } finally {
      setProfileLoading(false);
    }
  }, [user, toast]);

  const refreshCredits = useCallback(async () => {
    if (!user) return;

    setCreditsLoading(true);
    try {
      const { data: credits, error } = await supabase
        .from('credits')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error fetching credits:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch credits data',
          variant: 'destructive',
        });
      } else {
        setCredits(credits);
      }
    } catch (error) {
      console.error('Error fetching credits:', error);
    } finally {
      setCreditsLoading(false);
    }
  }, [user, toast]);

  const refreshStats = useCallback(async () => {
    if (!user) return;

    setStatsLoading(true);
    try {
      // Fetch documents count
      const { count: documentsCount } = await supabase
        .from('documents')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id);

      // Fetch extractions count
      const { count: extractionsCount } = await supabase
        .from('extractions')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id);

      setStats({
        documentsProcessed: documentsCount || 0,
        dataPointsExtracted: (extractionsCount || 0) * 8, // Approximate data points per extraction
        exportsGenerated: Math.floor((documentsCount || 0) / 3), // Simulated export count
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch statistics',
        variant: 'destructive',
      });
    } finally {
      setStatsLoading(false);
    }
  }, [user, toast]);

  const refreshAll = useCallback(async () => {
    await Promise.all([refreshProfile(), refreshCredits(), refreshStats()]);
  }, [refreshProfile, refreshCredits, refreshStats]);

  const signOut = useCallback(async () => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      toast({
        title: 'Error',
        description: 'Failed to sign out',
        variant: 'destructive',
      });
      throw error;
    }
  }, [toast]);

  const value: UserContextType = {
    user,
    isLoading,
    profile,
    credits,
    stats,
    profileLoading,
    creditsLoading,
    statsLoading,
    refreshProfile,
    refreshCredits,
    refreshStats,
    refreshAll,
    signOut,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};
